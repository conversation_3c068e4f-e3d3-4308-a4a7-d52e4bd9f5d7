# Hướng dẫn tích hợp MoMo Payment Gateway

## Tổng quan
Dự án đã được tích hợp thành công với MoMo Payment Gateway sử dụng API test environment.

## Cấu hình đã thêm

### 1. C<PERSON>u hình trong appsettings.json
```json
"MoMo": {
  "PartnerCode": "MOMO",
  "AccessKey": "F8BBA842ECF85",
  "SecretKey": "K95I86PE1waDM16U0xX08PD3vg6EKVlz",
  "ApiUrl": "https://test-payment.momo.vn/gw_payment/transactionProcessor",
  "ReturnUrl": "https://localhost:5057/Checkout/PaymentCallBack",
  "NotifyUrl": "https://localhost:5057/Checkout/MomoNotify",
  "RequestType": "captureMoMoWallet"
}
```

### 2. Models đã tạo
- `MoMoModels.cs`: Chứa tất cả models cần thiết cho MoMo API
  - MoMoConfig
  - MoMoPaymentRequest/Response
  - MoMoIpnRequest/Response
  - MoMoQueryRequest/Response
  - MoMoRefundRequest/Response

### 3. Services đã tạo
- `MoMoHelper.cs`: Service chính xử lý MoMo API
  - Tạo payment request
  - Xử lý signature (HMAC-SHA256)
  - Gọi MoMo API
  - Validate callback

### 4. Controllers đã cập nhật
- `PaymentService.cs`: Đã thay thế demo implementation bằng MoMo thực tế
- `CheckoutController.cs`: Controller mới xử lý MoMo callbacks

## Luồng thanh toán MoMo

### 1. Tạo thanh toán
1. User chọn phương thức thanh toán MoMo
2. System tạo MoMoPaymentRequest với signature
3. Gọi MoMo API để lấy payment URL
4. Redirect user đến MoMo payment page

### 2. Xử lý callback
1. User hoàn thành thanh toán trên MoMo
2. MoMo redirect về `ReturnUrl`: `/Checkout/PaymentCallBack`
3. System validate signature và xử lý kết quả
4. Redirect user đến trang success/failure

### 3. Xử lý IPN (Instant Payment Notification)
1. MoMo gửi POST request đến `NotifyUrl`: `/Checkout/MomoNotify`
2. System validate signature và cập nhật trạng thái thanh toán
3. Trả về response cho MoMo

## Endpoints mới

### GET /Checkout/PaymentCallBack
- Xử lý callback từ MoMo sau khi user thanh toán
- Validate signature và redirect đến trang kết quả

### POST /Checkout/MomoNotify
- Nhận IPN từ MoMo
- Validate signature và cập nhật database
- Trả về response cho MoMo

## Test tích hợp

### 1. Test cơ bản
```bash
# Build project
dotnet build

# Run project
dotnet run
```

### 2. Test thanh toán
1. Truy cập trang đặt vé
2. Chọn phương thức thanh toán MoMo
3. Kiểm tra redirect đến MoMo test environment
4. Sử dụng thông tin test của MoMo để thanh toán
5. Kiểm tra callback và cập nhật trạng thái

### 3. Thông tin test MoMo
- Môi trường: Test (sandbox)
- URL: https://test-payment.momo.vn
- Sử dụng app MoMo test hoặc thông tin test được cung cấp

## Lưu ý quan trọng

### 1. Security
- Signature được tạo bằng HMAC-SHA256
- Luôn validate signature trong callback và IPN
- SecretKey không được expose ra client

### 2. Error Handling
- Xử lý các mã lỗi từ MoMo
- Log đầy đủ để debug
- Fallback khi API MoMo không khả dụng

### 3. Production
- Thay đổi URL từ test sang production
- Cập nhật PartnerCode, AccessKey, SecretKey production
- Cập nhật ReturnUrl và NotifyUrl với domain thực tế

## Mã lỗi MoMo thường gặp
- `0`: Thành công
- `9000`: Giao dịch được xác nhận thành công
- `8000`: Giao dịch đang được xử lý
- `7000`: Giao dịch bị từ chối bởi người dùng
- `1006`: Giao dịch thất bại do tài khoản người dùng chưa được kích hoạt
- `2001`: Giao dịch thất bại do sai thông tin
- `99`: Lỗi không xác định

## Monitoring và Logging
- Tất cả requests/responses được log
- Monitor callback success rate
- Track payment completion time
- Alert khi có lỗi signature validation
