using System.ComponentModel.DataAnnotations;

namespace DatVeXe.Models
{
    // MoMo Configuration Model
    public class MoMoConfig
    {
        public string PartnerCode { get; set; } = string.Empty;
        public string AccessKey { get; set; } = string.Empty;
        public string SecretKey { get; set; } = string.Empty;
        public string ApiUrl { get; set; } = string.Empty;
        public string ReturnUrl { get; set; } = string.Empty;
        public string NotifyUrl { get; set; } = string.Empty;
        public string RequestType { get; set; } = string.Empty;
    }

    // MoMo Payment Request Model
    public class MoMoPaymentRequest
    {
        public string partnerCode { get; set; } = string.Empty;
        public string partnerName { get; set; } = "Hệ thống đặt vé xe";
        public string storeId { get; set; } = "MomoTestStore";
        public string requestId { get; set; } = string.Empty;
        public long amount { get; set; }
        public string orderId { get; set; } = string.Empty;
        public string orderInfo { get; set; } = string.Empty;
        public string redirectUrl { get; set; } = string.Empty;
        public string ipnUrl { get; set; } = string.Empty;
        public string lang { get; set; } = "vi";
        public string requestType { get; set; } = string.Empty;
        public bool autoCapture { get; set; } = true;
        public string extraData { get; set; } = string.Empty;
        public string signature { get; set; } = string.Empty;
    }

    // MoMo Payment Response Model
    public class MoMoPaymentResponse
    {
        public string partnerCode { get; set; } = string.Empty;
        public string requestId { get; set; } = string.Empty;
        public string orderId { get; set; } = string.Empty;
        public long amount { get; set; }
        public long responseTime { get; set; }
        public string message { get; set; } = string.Empty;
        public string resultCode { get; set; } = string.Empty;
        public string payUrl { get; set; } = string.Empty;
        public string deeplink { get; set; } = string.Empty;
        public string qrCodeUrl { get; set; } = string.Empty;
        public string signature { get; set; } = string.Empty;
    }

    // MoMo IPN (Instant Payment Notification) Model
    public class MoMoIpnRequest
    {
        public string partnerCode { get; set; } = string.Empty;
        public string orderId { get; set; } = string.Empty;
        public string requestId { get; set; } = string.Empty;
        public long amount { get; set; }
        public string orderInfo { get; set; } = string.Empty;
        public string orderType { get; set; } = string.Empty;
        public long transId { get; set; }
        public string resultCode { get; set; } = string.Empty;
        public string message { get; set; } = string.Empty;
        public string payType { get; set; } = string.Empty;
        public long responseTime { get; set; }
        public string extraData { get; set; } = string.Empty;
        public string signature { get; set; } = string.Empty;
    }

    // MoMo IPN Response Model
    public class MoMoIpnResponse
    {
        public string partnerCode { get; set; } = string.Empty;
        public string requestId { get; set; } = string.Empty;
        public string orderId { get; set; } = string.Empty;
        public string resultCode { get; set; } = string.Empty;
        public string message { get; set; } = string.Empty;
        public long responseTime { get; set; }
        public string extraData { get; set; } = string.Empty;
        public string signature { get; set; } = string.Empty;
    }

    // MoMo Query Transaction Request Model
    public class MoMoQueryRequest
    {
        public string partnerCode { get; set; } = string.Empty;
        public string requestId { get; set; } = string.Empty;
        public string orderId { get; set; } = string.Empty;
        public string lang { get; set; } = "vi";
        public string signature { get; set; } = string.Empty;
    }

    // MoMo Query Transaction Response Model
    public class MoMoQueryResponse
    {
        public string partnerCode { get; set; } = string.Empty;
        public string requestId { get; set; } = string.Empty;
        public string orderId { get; set; } = string.Empty;
        public long amount { get; set; }
        public long transId { get; set; }
        public string resultCode { get; set; } = string.Empty;
        public string message { get; set; } = string.Empty;
        public long responseTime { get; set; }
        public string extraData { get; set; } = string.Empty;
        public string signature { get; set; } = string.Empty;
    }

    // MoMo Refund Request Model
    public class MoMoRefundRequest
    {
        public string partnerCode { get; set; } = string.Empty;
        public string requestId { get; set; } = string.Empty;
        public string orderId { get; set; } = string.Empty;
        public long amount { get; set; }
        public long transId { get; set; }
        public string lang { get; set; } = "vi";
        public string description { get; set; } = string.Empty;
        public string signature { get; set; } = string.Empty;
    }

    // MoMo Refund Response Model
    public class MoMoRefundResponse
    {
        public string partnerCode { get; set; } = string.Empty;
        public string requestId { get; set; } = string.Empty;
        public string orderId { get; set; } = string.Empty;
        public long amount { get; set; }
        public long transId { get; set; }
        public string resultCode { get; set; } = string.Empty;
        public string message { get; set; } = string.Empty;
        public long responseTime { get; set; }
        public string extraData { get; set; } = string.Empty;
        public string signature { get; set; } = string.Empty;
    }
}
