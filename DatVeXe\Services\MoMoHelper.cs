using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using DatVeXe.Models;
using Microsoft.Extensions.Options;

namespace DatVeXe.Services
{
    public interface IMoMoHelper
    {
        Task<MoMoPaymentResponse> CreatePaymentAsync(MoMoPaymentRequest request);
        Task<MoMoQueryResponse> QueryTransactionAsync(string orderId, string requestId);
        Task<MoMoRefundResponse> RefundTransactionAsync(MoMoRefundRequest request);
        bool ValidateSignature(string signature, string rawData);
        string CreateSignature(string rawData);
        MoMoPaymentRequest CreatePaymentRequest(string orderId, long amount, string orderInfo, string returnUrl, string notifyUrl);
    }

    public class MoMoHelper : IMoMoHelper
    {
        private readonly MoMoConfig _config;
        private readonly HttpClient _httpClient;
        private readonly ILogger<MoMoHelper> _logger;

        public MoMoHelper(IOptions<MoMoConfig> config, HttpClient httpClient, ILogger<MoMoHelper> logger)
        {
            _config = config.Value;
            _httpClient = httpClient;
            _logger = logger;
        }

        public MoMoPaymentRequest CreatePaymentRequest(string orderId, long amount, string orderInfo, string returnUrl, string notifyUrl)
        {
            var requestId = Guid.NewGuid().ToString();

            var request = new MoMoPaymentRequest
            {
                partnerCode = _config.PartnerCode,
                accessKey = _config.AccessKey,
                requestId = requestId,
                amount = amount.ToString(),
                orderId = orderId,
                orderInfo = orderInfo,
                returnUrl = returnUrl,
                notifyUrl = notifyUrl,
                requestType = _config.RequestType,
                extraData = ""
            };

            // Tạo raw signature data theo thứ tự chính xác của MoMo API
            var rawSignature = $"partnerCode={request.partnerCode}" +
                             $"&accessKey={_config.AccessKey}" +
                             $"&requestId={request.requestId}" +
                             $"&amount={request.amount}" +
                             $"&orderId={request.orderId}" +
                             $"&orderInfo={request.orderInfo}" +
                             $"&returnUrl={request.returnUrl}" +
                             $"&notifyUrl={request.notifyUrl}" +
                             $"&extraData={request.extraData}";

            _logger.LogInformation($"Raw signature data: {rawSignature}");
            _logger.LogInformation($"Secret key: {_config.SecretKey}");

            // Test các secret key khác nhau
            TestSignatures(rawSignature);

            request.signature = CreateSignature(rawSignature);

            _logger.LogInformation($"Generated signature: {request.signature}");

            return request;
        }

        public async Task<MoMoPaymentResponse> CreatePaymentAsync(MoMoPaymentRequest request)
        {
            try
            {
                // Serialize without camelCase to match MoMo API exactly
                var json = JsonSerializer.Serialize(request);

                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogInformation($"Sending MoMo payment request: {json}");

                var response = await _httpClient.PostAsync(_config.ApiUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation($"MoMo payment response: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    var momoResponse = JsonSerializer.Deserialize<MoMoPaymentResponse>(responseContent);

                    return momoResponse ?? new MoMoPaymentResponse { resultCode = "99", message = "Invalid response format" };
                }
                else
                {
                    _logger.LogError($"MoMo API error: {response.StatusCode} - {responseContent}");
                    return new MoMoPaymentResponse
                    {
                        resultCode = "99",
                        message = $"API Error: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling MoMo API");
                return new MoMoPaymentResponse
                {
                    resultCode = "99",
                    message = $"Exception: {ex.Message}"
                };
            }
        }

        public async Task<MoMoQueryResponse> QueryTransactionAsync(string orderId, string requestId)
        {
            try
            {
                var queryRequest = new MoMoQueryRequest
                {
                    partnerCode = _config.PartnerCode,
                    requestId = requestId,
                    orderId = orderId,
                    lang = "vi"
                };

                // Tạo signature cho query request
                var rawSignature = $"accessKey={_config.AccessKey}" +
                                 $"&orderId={queryRequest.orderId}" +
                                 $"&partnerCode={queryRequest.partnerCode}" +
                                 $"&requestId={queryRequest.requestId}";

                queryRequest.signature = CreateSignature(rawSignature);

                var json = JsonSerializer.Serialize(queryRequest, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_config.ApiUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var queryResponse = JsonSerializer.Deserialize<MoMoQueryResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    return queryResponse ?? new MoMoQueryResponse { resultCode = "99", message = "Invalid response format" };
                }
                else
                {
                    return new MoMoQueryResponse 
                    { 
                        resultCode = "99", 
                        message = $"API Error: {response.StatusCode}" 
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying MoMo transaction");
                return new MoMoQueryResponse 
                { 
                    resultCode = "99", 
                    message = $"Exception: {ex.Message}" 
                };
            }
        }

        public async Task<MoMoRefundResponse> RefundTransactionAsync(MoMoRefundRequest request)
        {
            try
            {
                // Tạo signature cho refund request
                var rawSignature = $"accessKey={_config.AccessKey}" +
                                 $"&amount={request.amount}" +
                                 $"&description={request.description}" +
                                 $"&orderId={request.orderId}" +
                                 $"&partnerCode={request.partnerCode}" +
                                 $"&requestId={request.requestId}" +
                                 $"&transId={request.transId}";

                request.signature = CreateSignature(rawSignature);

                var json = JsonSerializer.Serialize(request, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_config.ApiUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var refundResponse = JsonSerializer.Deserialize<MoMoRefundResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    return refundResponse ?? new MoMoRefundResponse { resultCode = "99", message = "Invalid response format" };
                }
                else
                {
                    return new MoMoRefundResponse 
                    { 
                        resultCode = "99", 
                        message = $"API Error: {response.StatusCode}" 
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing MoMo refund");
                return new MoMoRefundResponse 
                { 
                    resultCode = "99", 
                    message = $"Exception: {ex.Message}" 
                };
            }
        }

        public string CreateSignature(string rawData)
        {
            try
            {
                var keyBytes = Encoding.UTF8.GetBytes(_config.SecretKey);
                var dataBytes = Encoding.UTF8.GetBytes(rawData);

                using (var hmac = new HMACSHA256(keyBytes))
                {
                    var hashBytes = hmac.ComputeHash(dataBytes);
                    return Convert.ToHexString(hashBytes).ToLower();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating signature for data: {rawData}");
                throw;
            }
        }

        // Test method để thử các secret key khác nhau
        public void TestSignatures(string rawData)
        {
            var testKeys = new[]
            {
                "K951I86PE1waDM16U0xX08PD3vg6EKVlz",
                "K95I86PE1waDM16U0xX08PD3vg6EKVlz",
                "K951I86PE1waDM16U0xX08PD3vg6EKVlz",
                "at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa"
            };

            foreach (var key in testKeys)
            {
                try
                {
                    var keyBytes = Encoding.UTF8.GetBytes(key);
                    var dataBytes = Encoding.UTF8.GetBytes(rawData);

                    using (var hmac = new HMACSHA256(keyBytes))
                    {
                        var hashBytes = hmac.ComputeHash(dataBytes);
                        var signature = Convert.ToHexString(hashBytes).ToLower();
                        _logger.LogInformation($"Secret key: {key} -> Signature: {signature}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error with key: {key}");
                }
            }
        }

        public bool ValidateSignature(string signature, string rawData)
        {
            try
            {
                var expectedSignature = CreateSignature(rawData);
                return string.Equals(signature, expectedSignature, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error validating signature: {signature} for data: {rawData}");
                return false;
            }
        }
    }
}
