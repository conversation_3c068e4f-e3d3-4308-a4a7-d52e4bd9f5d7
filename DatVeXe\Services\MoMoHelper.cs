using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using DatVeXe.Models;
using Microsoft.Extensions.Options;

namespace DatVeXe.Services
{
    public interface IMoMoHelper
    {
        Task<MoMoPaymentResponse> CreatePaymentAsync(MoMoPaymentRequest request);
        Task<MoMoQueryResponse> QueryTransactionAsync(string orderId, string requestId);
        Task<MoMoRefundResponse> RefundTransactionAsync(MoMoRefundRequest request);
        bool ValidateSignature(string signature, string rawData);
        string CreateSignature(string rawData);
        MoMoPaymentRequest CreatePaymentRequest(string orderId, long amount, string orderInfo, string returnUrl, string notifyUrl);
    }

    public class MoMoHelper : IMoMoHelper
    {
        private readonly MoMoConfig _config;
        private readonly HttpClient _httpClient;
        private readonly ILogger<MoMoHelper> _logger;

        public MoMoHelper(IOptions<MoMoConfig> config, HttpClient httpClient, ILogger<MoMoHelper> logger)
        {
            _config = config.Value;
            _httpClient = httpClient;
            _logger = logger;
        }

        public MoMoPaymentRequest CreatePaymentRequest(string orderId, long amount, string orderInfo, string returnUrl, string notifyUrl)
        {
            var requestId = Guid.NewGuid().ToString();
            
            var request = new MoMoPaymentRequest
            {
                partnerCode = _config.PartnerCode,
                requestId = requestId,
                amount = amount,
                orderId = orderId,
                orderInfo = orderInfo,
                redirectUrl = returnUrl,
                ipnUrl = notifyUrl,
                requestType = _config.RequestType,
                extraData = ""
            };

            // Tạo raw signature data theo thứ tự alphabet
            var rawSignature = $"accessKey={_config.AccessKey}" +
                             $"&amount={request.amount}" +
                             $"&extraData={request.extraData}" +
                             $"&ipnUrl={request.ipnUrl}" +
                             $"&orderId={request.orderId}" +
                             $"&orderInfo={request.orderInfo}" +
                             $"&partnerCode={request.partnerCode}" +
                             $"&redirectUrl={request.redirectUrl}" +
                             $"&requestId={request.requestId}" +
                             $"&requestType={request.requestType}";

            request.signature = CreateSignature(rawSignature);

            return request;
        }

        public async Task<MoMoPaymentResponse> CreatePaymentAsync(MoMoPaymentRequest request)
        {
            try
            {
                var json = JsonSerializer.Serialize(request, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                _logger.LogInformation($"Sending MoMo payment request: {json}");

                var response = await _httpClient.PostAsync(_config.ApiUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation($"MoMo payment response: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    var momoResponse = JsonSerializer.Deserialize<MoMoPaymentResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    return momoResponse ?? new MoMoPaymentResponse { resultCode = "99", message = "Invalid response format" };
                }
                else
                {
                    _logger.LogError($"MoMo API error: {response.StatusCode} - {responseContent}");
                    return new MoMoPaymentResponse 
                    { 
                        resultCode = "99", 
                        message = $"API Error: {response.StatusCode}" 
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling MoMo API");
                return new MoMoPaymentResponse 
                { 
                    resultCode = "99", 
                    message = $"Exception: {ex.Message}" 
                };
            }
        }

        public async Task<MoMoQueryResponse> QueryTransactionAsync(string orderId, string requestId)
        {
            try
            {
                var queryRequest = new MoMoQueryRequest
                {
                    partnerCode = _config.PartnerCode,
                    requestId = requestId,
                    orderId = orderId,
                    lang = "vi"
                };

                // Tạo signature cho query request
                var rawSignature = $"accessKey={_config.AccessKey}" +
                                 $"&orderId={queryRequest.orderId}" +
                                 $"&partnerCode={queryRequest.partnerCode}" +
                                 $"&requestId={queryRequest.requestId}";

                queryRequest.signature = CreateSignature(rawSignature);

                var json = JsonSerializer.Serialize(queryRequest, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_config.ApiUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var queryResponse = JsonSerializer.Deserialize<MoMoQueryResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    return queryResponse ?? new MoMoQueryResponse { resultCode = "99", message = "Invalid response format" };
                }
                else
                {
                    return new MoMoQueryResponse 
                    { 
                        resultCode = "99", 
                        message = $"API Error: {response.StatusCode}" 
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying MoMo transaction");
                return new MoMoQueryResponse 
                { 
                    resultCode = "99", 
                    message = $"Exception: {ex.Message}" 
                };
            }
        }

        public async Task<MoMoRefundResponse> RefundTransactionAsync(MoMoRefundRequest request)
        {
            try
            {
                // Tạo signature cho refund request
                var rawSignature = $"accessKey={_config.AccessKey}" +
                                 $"&amount={request.amount}" +
                                 $"&description={request.description}" +
                                 $"&orderId={request.orderId}" +
                                 $"&partnerCode={request.partnerCode}" +
                                 $"&requestId={request.requestId}" +
                                 $"&transId={request.transId}";

                request.signature = CreateSignature(rawSignature);

                var json = JsonSerializer.Serialize(request, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_config.ApiUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var refundResponse = JsonSerializer.Deserialize<MoMoRefundResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    return refundResponse ?? new MoMoRefundResponse { resultCode = "99", message = "Invalid response format" };
                }
                else
                {
                    return new MoMoRefundResponse 
                    { 
                        resultCode = "99", 
                        message = $"API Error: {response.StatusCode}" 
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing MoMo refund");
                return new MoMoRefundResponse 
                { 
                    resultCode = "99", 
                    message = $"Exception: {ex.Message}" 
                };
            }
        }

        public string CreateSignature(string rawData)
        {
            try
            {
                var keyBytes = Encoding.UTF8.GetBytes(_config.SecretKey);
                var dataBytes = Encoding.UTF8.GetBytes(rawData);

                using (var hmac = new HMACSHA256(keyBytes))
                {
                    var hashBytes = hmac.ComputeHash(dataBytes);
                    return Convert.ToHexString(hashBytes).ToLower();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating signature for data: {rawData}");
                throw;
            }
        }

        public bool ValidateSignature(string signature, string rawData)
        {
            try
            {
                var expectedSignature = CreateSignature(rawData);
                return string.Equals(signature, expectedSignature, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error validating signature: {signature} for data: {rawData}");
                return false;
            }
        }
    }
}
